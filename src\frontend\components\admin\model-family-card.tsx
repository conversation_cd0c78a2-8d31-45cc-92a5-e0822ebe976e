import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  ChevronDownIcon,
  ChevronUpIcon,
  EditIcon,
  MoreHorizontalIcon,
  ToggleLeftIcon,
  ToggleRightIcon,
  TrashIcon,
} from "lucide-react";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Icons } from "@/components/ui/icons";

import { useTRPC } from "@/lib/trpc/client";
import { LLM_PROVIDER_DISPLAY_NAME, type LLM_Providers } from "@/shared/providers";
import { format } from "@/shared/utils";

type ModelFamily = {
  id: string;
  name: string;
  provider: LLM_Providers;
  models: string[];
  status: "enable" | "restricted" | "disabled";
  inputCost: number;
  outputCost: number;
  maxContext: number;
  maxOutput: number;
  ratelimitCost: number;
  createdAt: number;
  updatedAt: number;
};

type ModelFamilyCardProps = {
  family: ModelFamily;
};

export function ModelFamilyCard({ family }: ModelFamilyCardProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [showModels, setShowModels] = useState(false);
  const queryClient = useQueryClient();
  const trpc = useTRPC();
  const { mutateAsync: updateModelFamily } = useMutation(trpc.models.update.mutationOptions());
  const { mutateAsync: deleteModelFamily } = useMutation(trpc.models.delete.mutationOptions());

  const getStatusColor = (status: string) => {
    switch (status) {
      case "enable":
        return "bg-green-500";
      case "restricted":
        return "bg-yellow-500";
      case "disabled":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "enable":
        return "Enabled";
      case "restricted":
        return "Restricted";
      case "disabled":
        return "Disabled";
      default:
        return "Unknown";
    }
  };

  const handleToggleStatus = async () => {
    setIsUpdating(true);
    try {
      const newStatus = family.status === "enable" ? "disabled" : "enable";
      await updateModelFamily({
        id: family.id,
        status: newStatus,
      });
      queryClient.invalidateQueries({ queryKey: [["models", "list"]] });
    } catch (error) {
      console.error("Failed to update status:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (
      !confirm(`Are you sure you want to delete "${family.name}"? This action cannot be undone.`)
    ) {
      return;
    }

    setIsUpdating(true);
    try {
      await deleteModelFamily({ id: family.id });
      queryClient.invalidateQueries({ queryKey: [["models", "list"]] });
    } catch (error) {
      console.error("Failed to delete family:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const providerIcons: Record<LLM_Providers, React.ComponentType<{ className?: string }>> = {
    openai: Icons.openai,
    anthropic: Icons.anthropic,
    gemini: Icons.gemini,
    deepseek: Icons.deepseek,
    xai: Icons.xai,
    groq: Icons.groq,
  };

  const IconComponent = providerIcons[family.provider];

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <IconComponent className="h-6 w-6" />
              <div>
                <h3 className="font-semibold text-lg">{family.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {LLM_PROVIDER_DISPLAY_NAME[family.provider]}
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Status Badge */}
            <div className="flex items-center gap-2">
              <div className={`h-3 w-3 rounded-full ${getStatusColor(family.status)}`} />
              <span className="text-sm font-medium">{getStatusText(family.status)}</span>
            </div>

            {/* Actions Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontalIcon className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleToggleStatus} disabled={isUpdating}>
                  {family.status === "enable" ? (
                    <>
                      <ToggleLeftIcon className="h-4 w-4 mr-2" />
                      Disable
                    </>
                  ) : (
                    <>
                      <ToggleRightIcon className="h-4 w-4 mr-2" />
                      Enable
                    </>
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem disabled>
                  <EditIcon className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleDelete}
                  disabled={isUpdating}
                  className="text-red-600">
                  <TrashIcon className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {/* Input Cost */}
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground uppercase tracking-wide">Input Cost</p>
            <p className="font-semibold">{format.cost(family.inputCost)}</p>
          </div>

          {/* Output Cost */}
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground uppercase tracking-wide">Output Cost</p>
            <p className="font-semibold">{format.cost(family.outputCost)}</p>
          </div>

          {/* Max Context */}
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground uppercase tracking-wide">Max Context</p>
            <p className="font-semibold">{format.number(family.maxContext)}</p>
          </div>

          {/* Max Output */}
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground uppercase tracking-wide">Max Output</p>
            <p className="font-semibold">{format.number(family.maxOutput)}</p>
          </div>
        </div>

        {/* Models Section */}
        <div className="mt-4 pt-4 border-t space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Models:</span>
              <span className="font-medium">{family.models.length} configured</span>
              {family.models.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowModels(!showModels)}
                  className="h-6 w-6 p-0 hover:bg-accent">
                  {showModels ? (
                    <ChevronUpIcon className="h-3 w-3" />
                  ) : (
                    <ChevronDownIcon className="h-3 w-3" />
                  )}
                </Button>
              )}
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Rate limit cost:</span>
              <span className="font-medium">{family.ratelimitCost}</span>
            </div>
          </div>

          {/* Expandable Models List */}
          {showModels && family.models.length > 0 && (
            <div className="space-y-2">
              <div className="text-xs text-muted-foreground uppercase tracking-wide">
                Configured Model IDs
              </div>
              <div className="flex flex-wrap gap-1">
                {family.models.map((modelId) => (
                  <Badge key={modelId} variant="outline" className="text-xs font-mono">
                    {modelId}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* No models configured message */}
          {family.models.length === 0 && (
            <div className="text-xs text-muted-foreground italic">
              No models configured for this family
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
