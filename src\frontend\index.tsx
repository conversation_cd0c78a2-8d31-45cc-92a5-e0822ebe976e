import "./styles/globals.css";

import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { createBrowserRouter, RouterProvider } from "react-router";

import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";

import { HomePage } from "./routes/home";

import { AdminPage } from "./routes/admin";
import { AdminLayout } from "./routes/admin/_layout";
import { AdminDashboardPage } from "./routes/admin/dashboard";
import { AdminLoginPage } from "./routes/admin/login";
import { AdminModelsPage } from "./routes/admin/models";

import { TRPCProvider } from "@/lib/trpc/client";

const elem = document.getElementById("root")!;

const router = createBrowserRouter([
  { index: true, Component: HomePage },
  { path: "/admin/login", Component: AdminLoginPage },
  {
    path: "/admin",
    Component: AdminLayout,
    children: [
      { index: true, Component: AdminPage },
      { path: "dashboard", Component: AdminDashboardPage },
      { path: "users", Component: () => <div>Users</div> },
      { path: "models", Component: AdminModelsPage },
    ],
  },
]);

function Root() {
  return (
    <StrictMode>
      <ThemeProvider defaultTheme="dark" storageKey="ui-theme">
        <TRPCProvider>
          <RouterProvider router={router} />
        </TRPCProvider>

        <Toaster />
      </ThemeProvider>
    </StrictMode>
  );
}

const app = <Root />;

if (import.meta.hot) {
  const root = (import.meta.hot.data.root ??= createRoot(elem));
  root.render(app);
} else {
  createRoot(elem).render(app);
}
