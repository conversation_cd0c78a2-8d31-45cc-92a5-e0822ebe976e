import z from "zod";
import { protectedProcedure, t } from "..";
import { LLM_PROVIDERS } from "@/shared/providers";

export const modelsRouter = t.router({
  list: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db.query.modelFamilies.findMany();
  }),

  getModelIdsForProvider: protectedProcedure
    .input(z.object({ provider: z.literal(LLM_PROVIDERS) }))
    .query(async ({ ctx, input }) => {
      const families = await ctx.db.query.keys.findMany({
        where: (table, { eq }) => eq(table.provider, input.provider),
      });

      return families.map((f) => f.modelIds).flat();
    }),
});
