import z from "zod";
import { protectedProcedure, t } from "..";
import { LLM_PROVIDERS } from "@/shared/providers";
import { schema } from "@/shared/database";
import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";

const createModelFamilySchema = z.object({
  name: z.string().min(1, "Name is required"),
  provider: z.enum(LLM_PROVIDERS),
  models: z.array(z.string()).default([]),
  status: z.enum(["enable", "restricted", "disabled"]).default("disabled"),
  inputCost: z.number().int().min(0).default(0),
  outputCost: z.number().int().min(0).default(0),
  maxContext: z.number().int().min(0).default(0),
  maxOutput: z.number().int().min(0).default(0),
  ratelimitCost: z.number().int().min(1).default(1),
});

const updateModelFamilySchema = createModelFamilySchema.partial().extend({
  id: z.string(),
});

export const modelsRouter = t.router({
  list: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db.query.modelFamilies.findMany({
      orderBy: (table, { desc }) => [desc(table.createdAt)],
    });
  }),

  create: protectedProcedure.input(createModelFamilySchema).mutation(async ({ ctx, input }) => {
    const id = nanoid();

    await ctx.db.insert(schema.modelFamilies).values({
      id,
      ...input,
    });

    return { id };
  }),

  update: protectedProcedure.input(updateModelFamilySchema).mutation(async ({ ctx, input }) => {
    const { id, ...updateData } = input;

    await ctx.db
      .update(schema.modelFamilies)
      .set(updateData)
      .where(eq(schema.modelFamilies.id, id));

    return { success: true };
  }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.delete(schema.modelFamilies).where(eq(schema.modelFamilies.id, input.id));

      return { success: true };
    }),

  getModelIdsForProvider: protectedProcedure
    .input(z.object({ provider: z.enum(LLM_PROVIDERS) }))
    .query(async ({ ctx, input }) => {
      const families = await ctx.db.query.keys.findMany({
        where: (table, { eq }) => eq(table.provider, input.provider),
      });

      return families.map((f) => f.modelIds).flat();
    }),
});
