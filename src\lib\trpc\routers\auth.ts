import { z } from "zod";
import { TRPCError } from "@trpc/server";

import { db } from "@/shared/database";
import { users } from "@/shared/database/schema";

import {
  clearAuthCookies,
  getAccessFromCookie,
  getRefresh<PERSON>romCookie,
  issueTokenPair,
  setAuthCookies,
} from "@/shared/utils/jwt";

import { publicProcedure, t } from "../";

/**
 * Return minimal auth status derived from access token if present.
 */
export const authRouter = t.router({
  isAuthenticated: publicProcedure.query(async ({ ctx }) => {
    const access = await getAccessFromCookie(ctx.honoCtx);
    return { status: access ? "authenticated" : "unauthenticated" };
  }),

  login: publicProcedure
    .input(
      z.object({
        username: z.string().min(1).max(100).trim(),
        password: z.string().min(8).max(100),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const hasNoUser = (await db.$count(users)) === 0;

      // Find user
      const existing = await db.query.users.findFirst({
        where: (u, { eq }) => eq(u.username, input.username),
      });

      let userId: string | undefined = undefined;
      let passwordOk = false;

      if (!existing && hasNoUser) {
        const passwordHash = await Bun.password.hash(input.password, {
          algorithm: "bcrypt",
          cost: 12,
        });

        const inserted = await db
          .insert(users)
          .values({ username: input.username, passwordHash })
          .returning({ id: users.id });

        userId = inserted[0]?.id as string;
        passwordOk = true;
      } else if (existing) {
        userId = existing.id;
        passwordOk = await Bun.password.verify(input.password, existing.passwordHash);
      }

      if (!passwordOk || typeof userId === "undefined") {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "Invalid credentials" });
      }

      const pair = await issueTokenPair(userId, input.username);
      await setAuthCookies(ctx.honoCtx, pair);

      return { ok: true };
    }),

  refresh: publicProcedure.mutation(async ({ ctx }) => {
    const refresh = await getRefreshFromCookie(ctx.honoCtx);
    if (!refresh || refresh.type !== "refresh") {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "Invalid refresh token" });
    }

    const pair = await issueTokenPair(refresh.sub, refresh.username);
    await setAuthCookies(ctx.honoCtx, pair);

    return { ok: true };
  }),

  logout: publicProcedure.mutation(async ({ ctx }) => {
    await clearAuthCookies(ctx.honoCtx);
    return { ok: true };
  }),
});
