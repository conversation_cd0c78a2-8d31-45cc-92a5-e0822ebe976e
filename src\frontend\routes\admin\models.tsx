import { useQuery } from "@tanstack/react-query";
import { PlusIcon, SearchIcon } from "lucide-react";
import { useState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Icons } from "@/components/ui/icons";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { CreateModelFamilyDialog } from "@/components/admin/create-model-family-dialog";
import { ModelFamilyCard } from "@/components/admin/model-family-card";

import { useTRPC } from "@/lib/trpc/client";
import { LLM_PROVIDERS, LLM_PROVIDER_DISPLAY_NAME, type LLM_Providers } from "@/shared/providers";

type ModelFamily = {
  id: string;
  name: string;
  provider: LLM_Providers;
  models: string[];
  status: "enable" | "restricted" | "disabled";
  inputCost: number;
  outputCost: number;
  maxContext: number;
  maxOutput: number;
  ratelimitCost: number;
  createdAt: number;
  updatedAt: number;
};

type ModelFamilyStatus = "enable" | "restricted" | "disabled" | "all";

export function AdminModelsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedProvider, setSelectedProvider] = useState<LLM_Providers | "all">("all");
  const [selectedStatus, setSelectedStatus] = useState<ModelFamilyStatus>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const trpc = useTRPC();
  const { data: modelFamilies, isLoading } = useQuery(trpc.models.list.queryOptions());

  // Filter model families based on search and filters
  const filteredFamilies =
    modelFamilies?.filter((family: ModelFamily) => {
      const matchesSearch = family.name.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesProvider = selectedProvider === "all" || family.provider === selectedProvider;
      const matchesStatus = selectedStatus === "all" || family.status === selectedStatus;

      return matchesSearch && matchesProvider && matchesStatus;
    }) ?? [];

  // Statistics
  const totalFamilies = modelFamilies?.length || 0;
  const enabledFamilies =
    modelFamilies?.filter((f: ModelFamily) => f.status === "enable").length || 0;
  const restrictedFamilies =
    modelFamilies?.filter((f: ModelFamily) => f.status === "restricted").length || 0;

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Model Family Management</h1>
          <p className="text-muted-foreground">
            Manage your AI model families and their configurations
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Families</CardTitle>
            <Icons.logo className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalFamilies}</div>
            <p className="text-xs text-muted-foreground">Across all providers</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Enabled Families</CardTitle>
            <div className="h-4 w-4 rounded-full bg-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{enabledFamilies}</div>
            <p className="text-xs text-muted-foreground">Available to users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Restricted Families</CardTitle>
            <div className="h-4 w-4 rounded-full bg-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{restrictedFamilies}</div>
            <p className="text-xs text-muted-foreground">User access controls</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Most Used</CardTitle>
            <div className="h-4 w-4 rounded-full bg-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">N/A</div>
            <p className="text-xs text-muted-foreground">0 requests</p>
          </CardContent>
        </Card>
      </div>

      {/* Filter Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              {/* Search */}
              <div className="relative flex-1 max-w-sm">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search families by name..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Provider Filter */}
              <Select
                value={selectedProvider}
                onValueChange={(value) => setSelectedProvider(value as LLM_Providers | "all")}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Provider" />
                </SelectTrigger>

                <SelectContent>
                  <SelectItem value="all">All Providers</SelectItem>
                  {LLM_PROVIDERS.map((provider) => (
                    <SelectItem key={provider} value={provider}>
                      {LLM_PROVIDER_DISPLAY_NAME[provider]}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Status Filter */}
              <Select
                value={selectedStatus}
                onValueChange={(value) =>
                  setSelectedStatus(value as "enable" | "restricted" | "disabled" | "all")
                }>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>

                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="enable">Enabled</SelectItem>
                  <SelectItem value="restricted">Restricted</SelectItem>
                  <SelectItem value="disabled">Disabled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Create Button */}
            <Button onClick={() => setIsCreateDialogOpen(true)} className="flex items-center gap-2">
              <PlusIcon className="h-4 w-4" />
              Create New Family
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Model Families List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="text-muted-foreground">Loading model families...</div>
          </div>
        ) : filteredFamilies.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-muted-foreground">
              {modelFamilies?.length === 0
                ? "No model families found. Create your first one!"
                : "No families match your current filters."}
            </div>
          </div>
        ) : (
          filteredFamilies.map((family) => <ModelFamilyCard key={family.id} family={family} />)
        )}
      </div>

      {/* Create Dialog */}
      <CreateModelFamilyDialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen} />
    </div>
  );
}
