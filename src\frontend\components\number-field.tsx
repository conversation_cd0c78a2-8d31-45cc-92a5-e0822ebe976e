import { NumberField as BaseNumber<PERSON>ield } from "@base-ui-components/react/number-field";
import { MinusIcon, PlusIcon } from "lucide-react";
import * as React from "react";

type NumberFieldProps = {
  title: string;
  step: number;
  min?: number;
  max?: number;

  name: string;
  value?: number | null;
  defaultValue?: number;
  placeholder?: string;
  onValueChange?: (value: number | null, event: Event | undefined) => void;
  className?: string;
};

export function NumberField({ placeholder, ...props }: NumberFieldProps) {
  const id = React.useId();

  return (
    <BaseNumberField.Root id={id} {...props} snapOnStep className="flex flex-col items-start gap-1">
      <BaseNumberField.ScrubArea className="cursor-ew-resize">
        <label htmlFor={id} className="cursor-ew-resize text-sm font-medium text-gray-900">
          {props.title}
        </label>

        <BaseNumberField.ScrubAreaCursor className="drop-shadow-[0_1px_1px_#0008] filter">
          <CursorGrowIcon />
        </BaseNumberField.ScrubAreaCursor>
      </BaseNumberField.ScrubArea>

      <BaseNumberField.Group className="flex">
        <BaseNumberField.Decrement className="flex size-10 items-center justify-center rounded-tr-md rounded-br-md border border-gray-200 bg-gray-50 bg-clip-padding text-gray-900 select-none hover:bg-gray-100 active:bg-gray-100">
          <MinusIcon />
        </BaseNumberField.Decrement>

        <BaseNumberField.Input
          className="h-10 w-24 border-t border-b border-gray-200 text-center text-base text-gray-900 tabular-nums focus:z-1"
          placeholder={placeholder}
        />

        <BaseNumberField.Increment className="flex size-10 items-center justify-center rounded-tr-md rounded-br-md border border-gray-200 bg-gray-50 bg-clip-padding text-gray-900 select-none hover:bg-gray-100 active:bg-gray-100">
          <PlusIcon />
        </BaseNumberField.Increment>
      </BaseNumberField.Group>
    </BaseNumberField.Root>
  );
}

function CursorGrowIcon(props: React.ComponentProps<"svg">) {
  return (
    <svg
      width="26"
      height="14"
      viewBox="0 0 24 14"
      fill="black"
      stroke="white"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <path d="M19.5 5.5L6.49737 5.51844V2L1 6.9999L6.5 12L6.49737 8.5L19.5 8.5V12L25 6.9999L19.5 2V5.5Z" />
    </svg>
  );
}
