import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Icons } from "@/components/ui/icons";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { useTRPC } from "@/lib/trpc/client";
import { LLM_PROVIDERS, LLM_PROVIDER_DISPLAY_NAME, type LLM_Providers } from "@/shared/providers";
import { cn } from "@/shared/utils";

const createModelFamilySchema = z.object({
  name: z.string().min(1, "Name is required"),
  provider: z.enum(LLM_PROVIDERS),
  models: z.array(z.string()).min(1, "At least one model is required"),
  status: z.enum(["enable", "restricted", "disabled"]),
  inputCost: z.number().min(0, "Input cost must be non-negative"),
  outputCost: z.number().min(0, "Output cost must be non-negative"),
  maxContext: z.number().min(0, "Max context must be non-negative"),
  maxOutput: z.number().min(0, "Max output must be non-negative"),
  ratelimitCost: z.number().min(1, "Rate limit cost must be at least 1"),
});

type CreateModelFamilyForm = z.infer<typeof createModelFamilySchema>;

type CreateModelFamilyDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function CreateModelFamilyDialog({ open, onOpenChange }: CreateModelFamilyDialogProps) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<CreateModelFamilyForm>({
    resolver: zodResolver(createModelFamilySchema),
    defaultValues: {
      status: "disabled",
      inputCost: 0,
      outputCost: 0,
      maxContext: 0,
      maxOutput: 0,
      ratelimitCost: 1,
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const selectedProvider = watch("provider");

  const { data: modelIds } = useQuery(
    trpc.models.getModelIdsForProvider.queryOptions(
      { provider: selectedProvider },
      { enabled: !!selectedProvider },
    ),
  );
  const { mutateAsync: createModelFamily } = useMutation(trpc.models.create.mutationOptions());

  const onSubmit = async (data: CreateModelFamilyForm) => {
    setIsSubmitting(true);
    try {
      await createModelFamily(data);
      queryClient.invalidateQueries({ queryKey: [["models", "list"]] });
      reset();
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to create model family:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onOpenChange(false);
  };

  const formatCost = (cost: number) => {
    return `$${(cost / 1000000).toFixed(2)} / 1M`;
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="w-full">
        <DialogHeader>
          <DialogTitle>Create New Model Family</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Family Name</Label>
              <Input
                id="name"
                placeholder="e.g., GPT-4 Turbo"
                {...register("name")}
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
            </div>

            {/* Provider */}
            <div className="space-y-2">
              <Label htmlFor="provider">Provider</Label>
              <Select
                value={selectedProvider}
                onValueChange={(value) => setValue("provider", value as LLM_Providers)}>
                <SelectTrigger>
                  <SelectValue placeholder="Provider" />
                </SelectTrigger>

                <SelectContent>
                  {LLM_PROVIDERS.map((provider) => (
                    <SelectItem key={provider} value={provider}>
                      {LLM_PROVIDER_DISPLAY_NAME[provider]}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.provider && <p className="text-sm text-red-500">{errors.provider.message}</p>}
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={watch("status")}
                onValueChange={(value) =>
                  setValue("status", value as "enable" | "restricted" | "disabled")
                }>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>

                <SelectContent>
                  <SelectItem value="disabled">Disabled</SelectItem>
                  <SelectItem value="restricted">Restricted</SelectItem>
                  <SelectItem value="enable">Enabled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Rate Limit Cost */}
            <div className="space-y-2">
              <Label htmlFor="ratelimitCost">Rate Limit Cost</Label>
              <Input
                id="ratelimitCost"
                type="number"
                min="1"
                step="1"
                {...register("ratelimitCost", { valueAsNumber: true })}
                className={errors.ratelimitCost ? "border-red-500" : ""}
              />
              {errors.ratelimitCost && (
                <p className="text-sm text-red-500">{errors.ratelimitCost.message}</p>
              )}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium">Model Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Model IDs */}
              <div className="space-y-2"></div>
            </div>
          </div>

          {/* Cost Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Pricing Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Input Cost */}
              <div className="space-y-2">
                <Label htmlFor="inputCost" className="min-w-max">
                  Input Cost (per 1M tokens)
                </Label>
                <div className="relative">
                  <Input
                    id="inputCost"
                    type="number"
                    min="0"
                    step="0.01"
                    placeholder="0.00"
                    {...register("inputCost", { valueAsNumber: true })}
                    className={cn(errors.inputCost ? "border-red-500" : "", "appearance-none")}
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                    {formatCost(watch("inputCost") || 0)}
                  </div>
                </div>
                {errors.inputCost && (
                  <p className="text-sm text-red-500">{errors.inputCost.message}</p>
                )}
              </div>

              {/* Output Cost */}
              <div className="space-y-2">
                <Label htmlFor="outputCost" className="min-w-max">
                  Output Cost (per 1M tokens)
                </Label>
                <div className="relative">
                  <Input
                    id="outputCost"
                    type="number"
                    min="0"
                    step="0.01"
                    placeholder="0.00"
                    {...register("outputCost", { valueAsNumber: true })}
                    className={errors.outputCost ? "border-red-500" : ""}
                  />

                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                    {formatCost(watch("outputCost") || 0)}
                  </div>
                </div>
                {errors.outputCost && (
                  <p className="text-sm text-red-500">{errors.outputCost.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Limits Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Token Limits</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Max Context */}
              <div className="space-y-2">
                <Label htmlFor="maxContext">Max Context Tokens</Label>
                <Input
                  id="maxContext"
                  type="number"
                  min="0"
                  step="1"
                  placeholder="0"
                  {...register("maxContext", { valueAsNumber: true })}
                  className={errors.maxContext ? "border-red-500" : ""}
                />
                {errors.maxContext && (
                  <p className="text-sm text-red-500">{errors.maxContext.message}</p>
                )}
              </div>

              {/* Max Output */}
              <div className="space-y-2">
                <Label htmlFor="maxOutput">Max Output Tokens</Label>
                <Input
                  id="maxOutput"
                  type="number"
                  min="0"
                  step="1"
                  placeholder="0"
                  {...register("maxOutput", { valueAsNumber: true })}
                  className={errors.maxOutput ? "border-red-500" : ""}
                />
                {errors.maxOutput && (
                  <p className="text-sm text-red-500">{errors.maxOutput.message}</p>
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>

            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Family"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
