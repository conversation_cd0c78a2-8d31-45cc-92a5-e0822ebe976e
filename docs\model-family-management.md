# Model Family Management

This document describes the Model Family Management feature implemented in the AI Reverse Proxy admin interface.

## Overview

The Model Family Management page allows administrators to create, view, edit, and manage AI model families across different providers. Each model family represents a group of related AI models with shared configuration settings.

## Features

### 1. Dashboard Overview
- **Statistics Cards**: Display total families, enabled families, restricted families, and usage metrics
- **Real-time Data**: All statistics update automatically when families are modified

### 2. Filter and Search
- **Provider Filter**: Filter families by AI provider (OpenAI, Anthropic, Gemini, DeepSeek, XAI, Groq)
- **Status Filter**: Filter by family status (Enabled, Restricted, Disabled)
- **Search**: Search families by name with real-time filtering

### 3. Model Family Cards
Each family is displayed as a card showing:
- **Provider Icon**: Visual indicator of the AI provider
- **Family Name**: Descriptive name for the model family
- **Status Badge**: Current status with color coding
- **Cost Information**: Input and output costs formatted as `$X.XX / 1M` tokens
- **Token Limits**: Maximum context and output token limits
- **Model Count**: Number of configured models in the family
- **Rate Limit Cost**: Cost factor for rate limiting

### 4. Family Management Actions
- **Toggle Status**: Quick enable/disable functionality
- **Delete**: Remove families with confirmation dialog
- **Edit**: (Planned) Modify family settings

### 5. Create New Family Dialog
Comprehensive form for creating new model families:
- **Basic Information**: Name and provider selection
- **Status Configuration**: Set initial status
- **Pricing Setup**: Configure input/output costs per 1M tokens
- **Token Limits**: Set maximum context and output tokens
- **Rate Limiting**: Configure rate limit cost factor

## Technical Implementation

### Database Schema
Model families are stored with the following structure:
```typescript
type ModelFamily = {
  id: string;
  name: string;
  provider: LLM_Providers;
  models: string[];
  status: "enable" | "restricted" | "disabled";
  inputCost: number;    // Cost per 1M input tokens
  outputCost: number;   // Cost per 1M output tokens
  maxContext: number;   // Maximum context tokens
  maxOutput: number;    // Maximum output tokens
  ratelimitCost: number; // Rate limit cost factor
  createdAt: number;
  updatedAt: number;
};
```

### API Endpoints
The following TRPC endpoints are available:
- `models.list` - Retrieve all model families
- `models.create` - Create a new model family
- `models.update` - Update an existing model family
- `models.delete` - Delete a model family

### Components
- **AdminModelsPage**: Main page component with filtering and statistics
- **ModelFamilyCard**: Individual family display component
- **CreateModelFamilyDialog**: Form dialog for creating new families

## Usage

### Creating a Model Family
1. Navigate to Admin → Models
2. Click "Create New Family" button
3. Fill in the required information:
   - Family name (e.g., "GPT-4 Turbo")
   - Select provider
   - Set status (Disabled by default)
   - Configure pricing (costs per 1M tokens)
   - Set token limits
   - Set rate limit cost
4. Click "Create Family"

### Managing Existing Families
1. Use filters to find specific families
2. Use the dropdown menu on each card to:
   - Toggle enable/disable status
   - Delete the family (with confirmation)

### Filtering and Search
- Use the search bar to find families by name
- Select provider filter to show only specific providers
- Select status filter to show only enabled/restricted/disabled families
- Combine filters for precise results

## Cost Format
All costs are displayed in the format `$X.XX / 1M` representing the cost per 1 million tokens. This provides a clear, standardized way to compare pricing across different model families.

## Status Indicators
- **Green**: Enabled - Available to users
- **Yellow**: Restricted - Limited access with user controls
- **Gray**: Disabled - Not available to users

## Future Enhancements
- Edit functionality for existing families
- Bulk operations (enable/disable multiple families)
- Usage analytics and metrics
- Model assignment and management within families
- Advanced filtering options
- Export/import functionality
