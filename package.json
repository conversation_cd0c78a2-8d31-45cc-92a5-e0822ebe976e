{"name": "bun-react-template", "version": "0.1.0", "private": true, "type": "module", "main": "src/server.tsx", "module": "src/server.tsx", "scripts": {"dev": "bun --hot src/server.tsx", "start": "NODE_ENV=production bun src/server.tsx", "build": "bun run build.ts", "typecheck": "tsgo --noEmit", "db:studio": "drizzle-kit studio --verbose", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "dependencies": {"@base-ui-components/react": "^1.0.0-beta.2", "@hono/trpc-server": "^0.4.0", "@hookform/resolvers": "^5.2.1", "@libsql/client": "^0.15.10", "@paralleldrive/cuid2": "^2.2.2", "@tanstack/react-query": "^5.84.1", "@trpc/client": "^11.4.3", "@trpc/server": "^11.4.3", "@trpc/tanstack-react-query": "^11.4.4", "bun-plugin-tailwind": "^0.0.15", "chalk": "^5.5.0", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.44.4", "hono": "^4.8.12", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.536.0", "microdiff": "^1.5.0", "next-themes": "^0.4.6", "node-schedule": "^2.1.1", "pino": "^9.7.0", "radix-ui": "^1.4.2", "react": "^19", "react-dom": "^19", "react-hook-form": "^7.62.0", "react-router": "^7.7.1", "redis": "^5.8.0", "sonner": "^2.0.7", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.15"}, "devDependencies": {"@types/bun": "^1.2.19", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.2.0", "@types/node-schedule": "^2.1.8", "@types/react": "^19", "@types/react-dom": "^19", "@typescript/native-preview": "^7.0.0-dev.20250806.1", "drizzle-kit": "^0.31.4", "pino-pretty": "^13.1.1", "typescript": "^5.9.2"}}