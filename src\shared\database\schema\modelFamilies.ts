import { sql } from "drizzle-orm";
import { index, integer, text } from "drizzle-orm/sqlite-core";

import { LLM_PROVIDERS } from "@/shared/providers";

import { sqliteTable } from "../helpers";

export const modelFamilies = sqliteTable(
  "modelFamilies",
  {
    id: text().primaryKey(),
    name: text().notNull().unique(),
    provider: text({ enum: LLM_PROVIDERS }).notNull(),
    models: text({ mode: "json" }).notNull().$type<string[]>().default([]),

    status: text({ enum: ["enable", "restricted", "disabled"] })
      .notNull()
      .default("disabled"),

    inputCost: integer().notNull().default(0),
    outputCost: integer().notNull().default(0),

    maxContext: integer().notNull().default(0),
    maxOutput: integer().notNull().default(0),

    ratelimitCost: integer().notNull().default(1),

    createdAt: integer()
      .default(sql`(strftime('%s', 'now'))`)
      .notNull(),
    updatedAt: integer()
      .default(sql`(strftime('%s', 'now'))`)
      .$onUpdate(() => sql`(strftime('%s', 'now'))`)
      .notNull(),
  },
  (table) => [index("idx_models_provider").on(table.provider)],
);
