import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { z } from "zod";

import { EyeIcon, EyeOffIcon, LockIcon, UserIcon } from "lucide-react";
import React from "react";
import { useForm } from "react-hook-form";
import { Navigate, useNavigate } from "react-router";

import { useTRPC } from "@/lib/trpc/client";
import { useMutation, useQuery } from "@tanstack/react-query";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

import { tryCatch } from "@/shared/utils/try-catch";

const schema = z.object({
  username: z.string().min(1, "Username is required").max(100, "Username is too long"),
  password: z
    .string({ error: "Password is required" })
    .min(8, "Password must be at least 8 characters"),
});

type FormValues = z.infer<typeof schema>;

function PasswordInput(props: React.InputHTMLAttributes<HTMLInputElement>) {
  const [visible, setVisible] = React.useState(false);

  const type = visible ? "text" : "password";
  const Icon = visible ? EyeOffIcon : EyeIcon;

  return (
    <div className="relative">
      <Input type={type} {...props} />

      <button
        type="button"
        aria-label={visible ? "Hide password" : "Show password"}
        className="absolute inset-y-0 right-0 flex items-center px-3 text-muted-foreground hover:text-foreground"
        onClick={() => setVisible((v) => !v)}>
        <Icon className="h-4 w-4" />
      </button>
    </div>
  );
}

export function AdminLoginPage() {
  const navigate = useNavigate();

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: { username: "test", password: "12345678" },
    mode: "onTouched",
  });

  const { handleSubmit, control, formState } = form;

  const trpc = useTRPC();

  const { data, isPending, refetch } = useQuery(trpc.auth.isAuthenticated.queryOptions());
  const { mutateAsync } = useMutation(trpc.auth.login.mutationOptions());

  if (isPending) return <div>Loading...</div>;
  if (data && data.status === "authenticated") return <Navigate to="/admin/dashboard" replace />;

  async function onSubmit(values: FormValues) {
    const [data, err] = await tryCatch(() => mutateAsync(values));
    if (err) return form.setError("password", { message: err.message });

    if (data.ok) {
      console.log("Login successful", data);
      await refetch();
    }
  }

  return (
    <div className="min-h-[calc(100dvh)] bg-gradient-to-b from-background to-muted/30">
      <div className="container mx-auto flex min-h-[calc(100dvh)] items-center justify-center px-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Welcome back</CardTitle>
            <CardDescription>Enter your credentials to access the dashboard</CardDescription>
          </CardHeader>

          <CardContent>
            <Form {...form}>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="inline-flex items-center gap-2">
                        <UserIcon className="h-5 text-muted-foreground" />
                        <span>Username</span>
                      </FormLabel>

                      <FormControl>
                        <Input placeholder="johndoe" autoComplete="username" {...field} />
                      </FormControl>

                      <FormDescription>This is your admin username.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="inline-flex items-center gap-2">
                        <LockIcon className="h-5 text-muted-foreground" />
                        <span>Password</span>
                      </FormLabel>

                      <FormControl>
                        <div className="relative">
                          <PasswordInput
                            placeholder="••••••••"
                            autoComplete="current-password"
                            {...field}
                          />
                        </div>
                      </FormControl>

                      <FormDescription>This is your admin password.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" className="w-full" disabled={formState.isSubmitting}>
                  {formState.isSubmitting ? "Signing in..." : "Sign in"}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
